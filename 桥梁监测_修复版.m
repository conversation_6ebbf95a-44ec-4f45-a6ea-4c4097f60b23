% 无人机桥梁位移测量程序（修复版：解决图像修正功能失效问题）
% 核心修复：修正累积位移计算逻辑，简化图像修正函数，确保正确的全局运动补偿
% 修复日期：基于需求分析和设计文档的完整修复方案
clear all; close all; clc;

%% 参数设置（强化跟踪稳定性）
imageFolder = 'H:\7.12;16.54(0)'; % 图像文件夹路径
savePath = fullfile(imageFolder, 'results'); 
if ~exist(savePath, 'dir'), mkdir(savePath); end 
imageExt = '*.jpg'; 
isCalibrated = false; 
scaleFactor = 1; 
frameRate = 30; 
unit = '毫米'; 
numFeaturePoints = 3;  % 外部特征点数量（用于估计无人机晃动）
numStructurePoints = 2;  % 桥梁结构点数量（用于测量位移）
templateSize = [15, 20, 25, 30];  % 增加模板尺度，提升匹配适应性
searchRange = 120;  % 扩大搜索范围（应对较大晃动）
corrThreshold = 0.25;  % 降低基础相关系数阈值
dynamicThreshold = true; 
ransacThreshold = 2.5;  % 放宽RANSAC阈值，提升全局运动估计包容性
maxRANSACIter = 1000;  % 增加RANSAC迭代次数
templateUpdateRate = 0.3;  % 提高模板更新率，适应环境变化
updateInterval = 2;  % 缩短更新间隔
maxFailCount = 6;  % 提高失败容忍次数
motionSmoothing = 0.2;  % 降低平滑系数，提升响应速度
driftCorrectionInterval = 15;  % 缩短漂移校正间隔
displacementSmoothingWindow = 3;  % 缩小平滑窗口，保留细节
saveEveryFrame = true; 

%% 读取图像序列
try
    imageFiles = dir(fullfile(imageFolder, imageExt));
    numFrames = length(imageFiles);
    if numFrames < 2
        error('图像数量过少（至少需要2张）');
    end
    [~, idx] = sort([imageFiles.datenum]);
    imageFiles = imageFiles(idx);  % 按时间排序
    
    fprintf('成功读取图像序列: %s\n', imageFolder);
    fprintf('图像总数: %d 张\n', numFrames);
    fprintf('帧率: %.2f FPS\n', frameRate);
    
    frame1 = imread(fullfile(imageFolder, imageFiles(1).name));
    if size(frame1,3) == 1
        frame1 = cat(3, frame1, frame1, frame1);  % 转为RGB
    end
    [frameH, frameW, ~] = size(frame1);
    grayFrame1 = rgb2gray(frame1);
catch ME
    fprintf('错误: 无法读取图像序列 %s\n', imageFolder);
    fprintf('详情: %s\n', ME.message);
    return;
end

%% 生成兼容的颜色（用于标记特征点）
colorNames = {'r', 'g', 'b', 'c', 'm', 'y', 'k'}; 
colorIdx = 1:(numFeaturePoints + numStructurePoints);
colorNames = colorNames(mod(colorIdx-1, length(colorNames)) + 1);

%% 尺度标定（优化版：增强用户引导）
if ~isCalibrated
    fprintf('\n=== 尺度标定 ===\n');
    figure('Name','尺度标定','NumberTitle','off');
    imshow(frame1);
    title('点击水平方向已知长度物体的两个端点（建议选择>5米的直线，纹理清晰）');
    hold on;
    text(10,20,'请点击第一个点（尽量选端点）','Color', colorNames{1}, 'FontSize',12,'BackgroundColor','white');
    
    try
        confirmed = false;
        while ~confirmed
            [x1, y1] = ginput(1);
            x1 = round(x1); y1 = round(y1);
            plot(x1, y1, 'o', 'MarkerSize', 8, 'LineWidth', 2, 'Color', colorNames{1});
            text(x1+5, y1+5, '点1', 'Color', colorNames{1}, 'FontSize',10);
            
            text(10,40,'请点击第二个点（同一直线另一端）','Color', colorNames{1}, 'FontSize',12,'BackgroundColor','white');
            [x2, y2] = ginput(1);
            x2 = round(x2); y2 = round(y2);
            plot(x2, y2, 'o', 'MarkerSize', 8, 'LineWidth', 2, 'Color', colorNames{1});
            text(x2+5, y2+5, '点2', 'Color', colorNames{1}, 'FontSize',10);
            line([x1,x2], [y1,y2], 'Color', colorNames{1}, 'LineWidth', 2);
            
            pixelDist = sqrt((x2-x1)^2 + (y2-y1)^2);
            if pixelDist < 250 
                warning('标定距离过短（建议>250像素），请重新选取');
                clf; imshow(frame1); hold on;
                title('点击水平方向已知长度物体的两个端点（建议选择>5米的直线，纹理清晰）');
                continue;
            end
            
            realDist = input('这两点的实际距离(毫米): ');
            if realDist <= 0
                error('实际距离必须为正数');
            end
            scaleFactor = realDist / pixelDist;  % 像素->毫米转换因子
            
            fprintf('水平尺度因子: %.6f 毫米/像素\n', scaleFactor);
            resp = input('是否确认此尺度？(y/n): ', 's');
            if strcmpi(resp, 'y')
                confirmed = true;
            else
                clf; imshow(frame1); hold on;
                title('点击水平方向已知长度物体的两个端点（建议选择>5米的直线，纹理清晰）');
            end
        end
        
        % 垂直标定优化（增加容错提示）
        title('点击垂直方向已知长度物体的两个端点（按ESC跳过，建议选桥墩/立柱）');
        text(10,60,'请点击垂直第一个点（尽量选上下端点）','Color', colorNames{2}, 'FontSize',12,'BackgroundColor','white');
        verticalCalibSuccess = false;
        try
            confirmedV = false;
            attemptCount = 0; 
            maxAttempts = 3; 
            
            while ~confirmedV && attemptCount < maxAttempts
                attemptCount = attemptCount + 1;
                [x3, y3] = ginput(1);
                x3 = round(x3); y3 = round(y3);
                plot(x3, y3, 'o', 'MarkerSize', 8, 'LineWidth', 2, 'Color', colorNames{2});
                text(x3+5, y3+5, '点3', 'Color', colorNames{2}, 'FontSize',10);
                
                text(10,80,'请点击垂直第二个点（同一直线另一端）','Color', colorNames{2}, 'FontSize',12,'BackgroundColor','white');
                [x4, y4] = ginput(1);
                x4 = round(x4); y4 = round(y4);
                plot(x4, y4, 'o', 'MarkerSize', 8, 'LineWidth', 2, 'Color', colorNames{2});
                text(x4+5, y4+5, '点4', 'Color', colorNames{2}, 'FontSize',10);
                line([x3,x4], [y3,y4], 'Color', colorNames{2}, 'LineWidth', 2);
                
                pixelDistV = sqrt((x4-x3)^2 + (y4-y3)^2);
                if pixelDistV < 200 
                    warning('垂直标定距离过短（建议>200像素），第%d次尝试', attemptCount);
                    clf; imshow(frame1); hold on;
                    title('点击垂直方向已知长度物体的两个端点（按ESC跳过，建议选桥墩/立柱）');
                    continue;
                end
                
                realDistV = input('这两点的实际垂直距离(毫米): ');
                if realDistV <= 0
                    error('实际距离必须为正数');
                end
                scaleFactorV = realDistV / pixelDistV;
                fprintf('垂直尺度因子: %.6f 毫米/像素\n', scaleFactorV);
                
                scaleDiff = abs(scaleFactorV - scaleFactor) / scaleFactor;
                fprintf('水平/垂直尺度差异: %.2f%%\n', scaleDiff*100);
                
                if scaleDiff > 0.1  % 差异超过10%时提示用户重新标定
                    warning('水平/垂直尺度差异>10%，建议重新选取标定对象');
                    resp = input('是否重新选取垂直标定？(y/n): ', 's');
                    if strcmpi(resp, 'y')
                        clf; imshow(frame1); hold on;
                        title('点击垂直方向已知长度物体的两个端点（按ESC跳过，建议选桥墩/立柱）');
                        continue;
                    else
                        scaleFactor = (scaleFactor + scaleFactorV) / 2;
                        fprintf('已取平均值作为尺度因子: %.6f 毫米/像素\n', scaleFactor);
                        confirmedV = true;
                        verticalCalibSuccess = true;
                    end
                else
                    resp = input('是否确认垂直尺度？(y/n): ', 's');
                    if strcmpi(resp, 'y')
                        confirmedV = true;
                        verticalCalibSuccess = true;
                    else
                        clf; imshow(frame1); hold on;
                        title('点击垂直方向已知长度物体的两个端点（按ESC跳过，建议选桥墩/立柱）');
                    end
                end
            end
            
            if ~verticalCalibSuccess && attemptCount >= maxAttempts
                fprintf('达到最大尝试次数（%d次），使用水平尺度因子\n', maxAttempts);
                verticalCalibSuccess = true;
                confirmedV = true;
            end
        catch
            fprintf('用户选择跳过垂直标定，使用水平尺度因子\n');
        end
        
        isCalibrated = true;
        close(gcf);
    catch ME
        fprintf('标定失败: %s\n', ME.message);
        fprintf('使用默认尺度（1像素=1%s）\n', unit);
    end
end

%% 选取外部特征点（优化纹理检查）
fprintf('\n=== 选取外部特征点（%d个） ===\n', numFeaturePoints);
figure('Name','外部特征点选取','NumberTitle','off');
imshow(frame1);
title('选取固定不动的参考点（如地面、山体，纹理清晰区域）');
hold on;
featPoints = zeros(numFeaturePoints, 2);  % 初始位置（第一帧）
featCurrentPos = zeros(numFeaturePoints, 2);  % 当前跟踪位置
featTemplates = cell(numFeaturePoints, length(templateSize));  % 多尺度模板
featTextureScore = zeros(numFeaturePoints, 1);  % 纹理评分
featReliability = ones(numFeaturePoints, 1);  % 可靠性评分

for k = 1:numFeaturePoints
    text(10,20+20*k, sprintf('请点击第%d个外部特征点（远离桥梁的静止区域）', k), ...
        'Color', colorNames{k}, 'FontSize',12,'BackgroundColor','white');
    while true
        [x, y] = ginput(1);
        x = round(x); y = round(y);
        margin = max(templateSize) + 8;  % 边界检查
        if x < margin || x > frameW - margin || ...
           y < margin || y > frameH - margin
            warning('特征点%d太靠近边界，请重新选取', k);
            continue;
        end
        % 纹理检查（降低阈值，适应更多场景）
        ts = max(templateSize);
        temp = imcrop(grayFrame1, [x-ts, y-ts, 2*ts, 2*ts]);
        [gx, gy] = imgradient(temp);
        gradMag = sqrt(gx.^2 + gy.^2);
        textureScore = var(gradMag(:));  % 纹理丰富度（值越高越好）
        if textureScore < 400  % 降低纹理阈值
            warning('特征点%d纹理不足（评分:%.1f < 400），请重新选取', k, textureScore);
            continue;
        end
        featTextureScore(k) = textureScore;
        break;
    end
    featPoints(k,:) = [x, y];
    featCurrentPos(k,:) = [x, y];
    % 保存多尺度模板
    for s = 1:length(templateSize)
        ts = templateSize(s);
        featTemplates{k,s} = imcrop(grayFrame1, [x-ts, y-ts, 2*ts, 2*ts]);
    end
    plot(x, y, 'o', 'MarkerSize', 10, 'LineWidth', 2, 'Color', colorNames{k});
    text(x+5, y+5, sprintf('特征点%d(纹理:%.0f)', k, featTextureScore(k)), 'Color', colorNames{k}, 'FontSize',10);
end
pause(1);
close(gcf);
