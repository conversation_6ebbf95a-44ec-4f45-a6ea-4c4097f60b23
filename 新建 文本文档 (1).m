% 无人机桥梁位移测量程序（优化版：提升跟踪稳定性）
% 核心优化：增强特征匹配鲁棒性，优化动态阈值，减少跟踪失败
clear all; close all; clc;

%% 参数设置（强化跟踪稳定性）
imageFolder = 'H:\7.12;16.54(0)'; % 图像文件夹路径
savePath = fullfile(imageFolder, 'results'); 
if ~exist(savePath, 'dir'), mkdir(savePath); end 
imageExt = '*.jpg'; 
isCalibrated = false; 
scaleFactor = 1; 
frameRate = 30; 
unit = '毫米'; 
numFeaturePoints = 3;  % 外部特征点数量（用于估计无人机晃动）
numStructurePoints = 2;  % 桥梁结构点数量（用于测量位移）
templateSize = [15, 20, 25, 30];  % 增加模板尺度，提升匹配适应性
searchRange = 120;  % 扩大搜索范围（应对较大晃动）
corrThreshold = 0.25;  % 降低基础相关系数阈值
dynamicThreshold = true; 
ransacThreshold = 2.5;  % 放宽RANSAC阈值，提升全局运动估计包容性
maxRANSACIter = 1000;  % 增加RANSAC迭代次数
templateUpdateRate = 0.3;  % 提高模板更新率，适应环境变化
updateInterval = 2;  % 缩短更新间隔
maxFailCount = 6;  % 提高失败容忍次数
motionSmoothing = 0.2;  % 降低平滑系数，提升响应速度
driftCorrectionInterval = 15;  % 缩短漂移校正间隔
displacementSmoothingWindow = 3;  % 缩小平滑窗口，保留细节
saveEveryFrame = true; 

%% 读取图像序列
try
    imageFiles = dir(fullfile(imageFolder, imageExt));
    numFrames = length(imageFiles);
    if numFrames < 2
        error('图像数量过少（至少需要2张）');
    end
    [~, idx] = sort([imageFiles.datenum]);
    imageFiles = imageFiles(idx);  % 按时间排序
    
    fprintf('成功读取图像序列: %s\n', imageFolder);
    fprintf('图像总数: %d 张\n', numFrames);
    fprintf('帧率: %.2f FPS\n', frameRate);
    
    frame1 = imread(fullfile(imageFolder, imageFiles(1).name));
    if size(frame1,3) == 1
        frame1 = cat(3, frame1, frame1, frame1);  % 转为RGB
    end
    [frameH, frameW, ~] = size(frame1);
    grayFrame1 = rgb2gray(frame1);
catch ME
    fprintf('错误: 无法读取图像序列 %s\n', imageFolder);
    fprintf('详情: %s\n', ME.message);
    return;
end

%% 生成兼容的颜色（用于标记特征点）
colorNames = {'r', 'g', 'b', 'c', 'm', 'y', 'k'}; 
colorIdx = 1:(numFeaturePoints + numStructurePoints);
colorNames = colorNames(mod(colorIdx-1, length(colorNames)) + 1);

%% 尺度标定（优化版：增强用户引导）
if ~isCalibrated
    fprintf('\n=== 尺度标定 ===\n');
    figure('Name','尺度标定','NumberTitle','off');
    imshow(frame1);
    title('点击水平方向已知长度物体的两个端点（建议选择>5米的直线，纹理清晰）');
    hold on;
    text(10,20,'请点击第一个点（尽量选端点）','Color', colorNames{1}, 'FontSize',12,'BackgroundColor','white');
    
    try
        confirmed = false;
        while ~confirmed
            [x1, y1] = ginput(1);
            x1 = round(x1); y1 = round(y1);
            plot(x1, y1, 'o', 'MarkerSize', 8, 'LineWidth', 2, 'Color', colorNames{1});
            text(x1+5, y1+5, '点1', 'Color', colorNames{1}, 'FontSize',10);
            
            text(10,40,'请点击第二个点（同一直线另一端）','Color', colorNames{1}, 'FontSize',12,'BackgroundColor','white');
            [x2, y2] = ginput(1);
            x2 = round(x2); y2 = round(y2);
            plot(x2, y2, 'o', 'MarkerSize', 8, 'LineWidth', 2, 'Color', colorNames{1});
            text(x2+5, y2+5, '点2', 'Color', colorNames{1}, 'FontSize',10);
            line([x1,x2], [y1,y2], 'Color', colorNames{1}, 'LineWidth', 2);
            
            pixelDist = sqrt((x2-x1)^2 + (y2-y1)^2);
            if pixelDist < 250 
                warning('标定距离过短（建议>250像素），请重新选取');
                clf; imshow(frame1); hold on;
                title('点击水平方向已知长度物体的两个端点（建议选择>5米的直线，纹理清晰）');
                continue;
            end
            
            realDist = input('这两点的实际距离(毫米): ');
            if realDist <= 0
                error('实际距离必须为正数');
            end
            scaleFactor = realDist / pixelDist;  % 像素->毫米转换因子
            
            fprintf('水平尺度因子: %.6f 毫米/像素\n', scaleFactor);
            resp = input('是否确认此尺度？(y/n): ', 's');
            if strcmpi(resp, 'y')
                confirmed = true;
            else
                clf; imshow(frame1); hold on;
                title('点击水平方向已知长度物体的两个端点（建议选择>5米的直线，纹理清晰）');
            end
        end
        
        % 垂直标定优化（增加容错提示）
        title('点击垂直方向已知长度物体的两个端点（按ESC跳过，建议选桥墩/立柱）');
        text(10,60,'请点击垂直第一个点（尽量选上下端点）','Color', colorNames{2}, 'FontSize',12,'BackgroundColor','white');
        verticalCalibSuccess = false;
        try
            confirmedV = false;
            attemptCount = 0; 
            maxAttempts = 3; 
            
            while ~confirmedV && attemptCount < maxAttempts
                attemptCount = attemptCount + 1;
                [x3, y3] = ginput(1);
                x3 = round(x3); y3 = round(y3);
                plot(x3, y3, 'o', 'MarkerSize', 8, 'LineWidth', 2, 'Color', colorNames{2});
                text(x3+5, y3+5, '点3', 'Color', colorNames{2}, 'FontSize',10);
                
                text(10,80,'请点击垂直第二个点（同一直线另一端）','Color', colorNames{2}, 'FontSize',12,'BackgroundColor','white');
                [x4, y4] = ginput(1);
                x4 = round(x4); y4 = round(y4);
                plot(x4, y4, 'o', 'MarkerSize', 8, 'LineWidth', 2, 'Color', colorNames{2});
                text(x4+5, y4+5, '点4', 'Color', colorNames{2}, 'FontSize',10);
                line([x3,x4], [y3,y4], 'Color', colorNames{2}, 'LineWidth', 2);
                
                pixelDistV = sqrt((x4-x3)^2 + (y4-y3)^2);
                if pixelDistV < 200 
                    warning('垂直标定距离过短（建议>200像素），第%d次尝试', attemptCount);
                    clf; imshow(frame1); hold on;
                    title('点击垂直方向已知长度物体的两个端点（按ESC跳过，建议选桥墩/立柱）');
                    continue;
                end
                
                realDistV = input('这两点的实际垂直距离(毫米): ');
                if realDistV <= 0
                    error('实际距离必须为正数');
                end
                scaleFactorV = realDistV / pixelDistV;
                fprintf('垂直尺度因子: %.6f 毫米/像素\n', scaleFactorV);
                
                scaleDiff = abs(scaleFactorV - scaleFactor) / scaleFactor;
                fprintf('水平/垂直尺度差异: %.2f%%\n', scaleDiff*100);
                
                if scaleDiff > 0.1  % 差异超过10%时提示用户重新标定
                    warning('水平/垂直尺度差异>10%，建议重新选取标定对象');
                    resp = input('是否重新选取垂直标定？(y/n): ', 's');
                    if strcmpi(resp, 'y')
                        clf; imshow(frame1); hold on;
                        title('点击垂直方向已知长度物体的两个端点（按ESC跳过，建议选桥墩/立柱）');
                        continue;
                    else
                        scaleFactor = (scaleFactor + scaleFactorV) / 2;
                        fprintf('已取平均值作为尺度因子: %.6f 毫米/像素\n', scaleFactor);
                        confirmedV = true;
                        verticalCalibSuccess = true;
                    end
                else
                    resp = input('是否确认垂直尺度？(y/n): ', 's');
                    if strcmpi(resp, 'y')
                        confirmedV = true;
                        verticalCalibSuccess = true;
                    else
                        clf; imshow(frame1); hold on;
                        title('点击垂直方向已知长度物体的两个端点（按ESC跳过，建议选桥墩/立柱）');
                    end
                end
            end
            
            if ~verticalCalibSuccess && attemptCount >= maxAttempts
                fprintf('达到最大尝试次数（%d次），使用水平尺度因子\n', maxAttempts);
                verticalCalibSuccess = true;
                confirmedV = true;
            end
        catch
            fprintf('用户选择跳过垂直标定，使用水平尺度因子\n');
        end
        
        isCalibrated = true;
        close(gcf);
    catch ME
        fprintf('标定失败: %s\n', ME.message);
        fprintf('使用默认尺度（1像素=1%s）\n', unit);
    end
end

%% 选取外部特征点（优化纹理检查）
fprintf('\n=== 选取外部特征点（%d个） ===\n', numFeaturePoints);
figure('Name','外部特征点选取','NumberTitle','off');
imshow(frame1);
title('选取固定不动的参考点（如地面、山体，纹理清晰区域）');
hold on;
featPoints = zeros(numFeaturePoints, 2);  % 初始位置（第一帧）
featCurrentPos = zeros(numFeaturePoints, 2);  % 当前跟踪位置
featTemplates = cell(numFeaturePoints, length(templateSize));  % 多尺度模板
featTextureScore = zeros(numFeaturePoints, 1);  % 纹理评分
featReliability = ones(numFeaturePoints, 1);  % 可靠性评分

for k = 1:numFeaturePoints
    text(10,20+20*k, sprintf('请点击第%d个外部特征点（远离桥梁的静止区域）', k), ...
        'Color', colorNames{k}, 'FontSize',12,'BackgroundColor','white');
    while true
        [x, y] = ginput(1);
        x = round(x); y = round(y);
        margin = max(templateSize) + 8;  % 边界检查
        if x < margin || x > frameW - margin || ...
           y < margin || y > frameH - margin
            warning('特征点%d太靠近边界，请重新选取', k);
            continue;
        end
        % 纹理检查（降低阈值，适应更多场景）
        ts = max(templateSize);
        temp = imcrop(grayFrame1, [x-ts, y-ts, 2*ts, 2*ts]);
        [gx, gy] = imgradient(temp);
        gradMag = sqrt(gx.^2 + gy.^2);
        textureScore = var(gradMag(:));  % 纹理丰富度（值越高越好）
        if textureScore < 400  % 降低纹理阈值
            warning('特征点%d纹理不足（评分:%.1f < 400），请重新选取', k, textureScore);
            continue;
        end
        featTextureScore(k) = textureScore;
        break;
    end
    featPoints(k,:) = [x, y];
    featCurrentPos(k,:) = [x, y];
    % 保存多尺度模板
    for s = 1:length(templateSize)
        ts = templateSize(s);
        featTemplates{k,s} = imcrop(grayFrame1, [x-ts, y-ts, 2*ts, 2*ts]);
    end
    plot(x, y, 'o', 'MarkerSize', 10, 'LineWidth', 2, 'Color', colorNames{k});
    text(x+5, y+5, sprintf('特征点%d(纹理:%.0f)', k, featTextureScore(k)), 'Color', colorNames{k}, 'FontSize',10);
end
pause(1);
close(gcf);

%% 选取结构点（优化纹理检查和提示）
fprintf('\n=== 选取结构点（%d个） ===\n', numStructurePoints);
figure('Name','结构点选取','NumberTitle','off');
imshow(frame1);
title('选取桥梁上的测量点（优先棱角、纹理丰富区域）');
hold on;
strucPoints = zeros(numStructurePoints, 2);  % 初始位置
strucCurrentPos = zeros(numStructurePoints, 2);  % 当前跟踪位置
strucTemplates = cell(numStructurePoints, length(templateSize));  % 多尺度模板
strucPrevPos = cell(numStructurePoints, 5);  % 历史位置（用于预测）
strucTextureScore = zeros(numStructurePoints, 1);  % 纹理评分

for k = 1:numStructurePoints
    strucHistDisplacement{k} = zeros(10, 2);  % 位移历史（用于动态阈值）
end

for k = 1:numStructurePoints
    text(10,20+20*k, sprintf('请点击第%d个结构点（如桥梁边角、标志物）', k), ...
        'Color', colorNames{numFeaturePoints + k}, 'FontSize',12,'BackgroundColor','white');
    while true
        [x, y] = ginput(1);
        x = round(x); y = round(y);
        margin = max(templateSize) + 8;  % 边界检查
        if x < margin || x > frameW - margin || ...
           y < margin || y > frameH - margin
            warning('结构点%d太靠近边界，请重新选取', k);
            continue;
        end
        % 纹理检查（降低阈值，增加提示）
        ts = max(templateSize);
        temp = imcrop(grayFrame1, [x-ts, y-ts, 2*ts, 2*ts]);
        [gx, gy] = imgradient(temp);
        gradMag = sqrt(gx.^2 + gy.^2);
        textureScore = var(gradMag(:));
        if textureScore < 600  % 降低纹理阈值至600
            warning('结构点%d纹理不足（评分:%.1f < 600），请选择棱角或纹理更丰富区域', k, textureScore);
            continue;
        end
        strucTextureScore(k) = textureScore;
        break;
    end
    strucPoints(k,:) = [x, y];
    strucCurrentPos(k,:) = [x, y];
    for p = 1:5
        strucPrevPos{k,p} = [x, y];  % 初始化历史位置
    end
    % 保存多尺度模板
    for s = 1:length(templateSize)
        ts = templateSize(s);
        strucTemplates{k,s} = imcrop(grayFrame1, [x-ts, y-ts, 2*ts, 2*ts]);
    end
    plot(x, y, 's', 'MarkerSize', 10, 'LineWidth', 2, 'Color', colorNames{numFeaturePoints + k});
    text(x+5, y+5, sprintf('结构点%d(纹理:%.0f)', k, strucTextureScore(k)), 'Color', colorNames{numFeaturePoints + k}, 'FontSize',10);
end
pause(1);
close(gcf);

%% 初始化变量（强化跟踪状态记录）
featFrameDisplacement = zeros(numFrames, numFeaturePoints, 2);  % 外部特征点每帧位移
globalMotion = zeros(numFrames, 2);  % 单帧全局运动（无人机晃动）
globalMotionSmoothed = zeros(numFrames, 2);  % 平滑后的单帧全局运动
% 修复：移除错误的累积位移变量，改为使用当前帧全局运动进行图像修正
correctedImages = cell(numFrames, 1);  % 修正后（消除晃动）的图像
markedImages = cell(numFrames, 1);  % 标记特征点的原始图像
featFailCount = zeros(1, numFeaturePoints);  % 特征点跟踪失败计数
featReliability = ones(numFeaturePoints, 1);  % 特征点可靠性

% 结构点测量相关变量
strucFrameDisplacement = zeros(numFrames, numStructurePoints, 2);
absoluteDisplacement = zeros(numFrames, numStructurePoints, 2);  % 消除晃动后的位移
absoluteDisplacementSmoothed = zeros(numFrames, numStructurePoints, 2);  % 平滑后位移
strucFailCount = zeros(1, numStructurePoints);
prevCorrVals = zeros(numStructurePoints, 5);  % 相关系数历史
velocityX = zeros(numStructurePoints, 1);  % 速度（用于预测）
velocityY = zeros(numStructurePoints, 1);
maxVelocity = 200;  % 提高最大速度限制
accelerationLimit = 60;  % 提高加速度限制

%% 多尺度匹配函数（优化搜索策略）
function [bestX, bestY, bestCorr] = multiScaleMatching(grayFrame, prevPos, templates, searchRange, templateSize)
    bestCorr = 0;
    bestX = prevPos(1);
    bestY = prevPos(2);
    x0 = prevPos(1);
    y0 = prevPos(2);
    
    % 定义搜索区域（扩大边缘容错）
    searchROI = [max(1, x0 - searchRange), max(1, y0 - searchRange), ...
        min(2*searchRange, size(grayFrame,2) - (x0 - searchRange)), ...
        min(2*searchRange, size(grayFrame,1) - (y0 - searchRange))];
    searchArea = imcrop(grayFrame, searchROI);
    if isempty(searchArea), return; end
    searchArea = double(searchArea);
    
    % 多尺度模板匹配（优先中等尺度模板）
    scaleWeights = [0.8, 1.0, 0.9, 0.7];  % 增加中等尺度权重（若4个尺度）
    for s = 1:length(templateSize)
        template = templates{s};
        if isempty(template), continue; end
        template = double(template);
        ts = size(template,1);
        if size(template,1) ~= size(template,2) || ts > size(searchArea,1)
            continue;
        end
        
        % 基于梯度的匹配（增加归一化）
        [tGradX, tGradY] = imgradient(template);
        templateNorm = sqrt(tGradX.^2 + tGradY.^2);
        templateNorm = templateNorm - mean(templateNorm(:));
        templateNorm = templateNorm / (norm(templateNorm(:)) + eps);
        
        [sGradX, sGradY] = imgradient(searchArea);
        searchNorm = sqrt(sGradX.^2 + sGradY.^2);
        searchNorm = searchNorm - mean(searchNorm(:));
        searchNorm = searchNorm / (norm(searchNorm(:)) + eps) * 0.5;  % 降低搜索区域权重
        
        corrMap = normxcorr2(templateNorm, searchNorm);
        corrMap = corrMap(ts:end-ts+1, ts:end-ts+1);  % 裁剪边缘噪声
        
        % 找最大相关系数（考虑局部最大值）
        [maxVal, maxIdx] = max(corrMap(:));
        [yIdx, xIdx] = ind2sub(size(corrMap), maxIdx);
        
        % 局部精细搜索（扩大搜索窗口）
        roiSize = 7;  % 从5扩大到7
        yStart = max(1, yIdx - roiSize);
        yEnd = min(size(corrMap,1), yIdx + roiSize);
        xStart = max(1, xIdx - roiSize);
        xEnd = min(size(corrMap,2), xIdx + roiSize);
        localMax = max(corrMap(yStart:yEnd, xStart:xEnd), [], 'all');
        if localMax > maxVal, maxVal = localMax; end
        
        % 转换回原始图像坐标
        x_i = searchROI(1) + xIdx - 1;
        y_i = searchROI(2) + yIdx - 1;
        x_i = max(1, min(x_i, size(grayFrame,2)));
        y_i = max(1, min(y_i, size(grayFrame,1)));
        
        % 保留最优匹配结果（结合尺度权重）
        weightedVal = maxVal * scaleWeights(s);
        if weightedVal > bestCorr + 0.005
            bestCorr = maxVal;
            bestX = x_i;
            bestY = y_i;
        end
    end
end

%% 加权平均函数（用于RANSAC中的模型估计）
function wMean = weightedMean(sample, weights)
    weights = weights(:) / sum(weights);
    wMean = zeros(1, size(sample, 2));
    for dim = 1:size(sample, 2)
        wMean(dim) = sum(sample(:, dim) .* weights);
    end
end

%% RANSAC滤波函数（优化权重计算）
function [avgDisp, inliers, reliability] = ransacMotionEstimation(displacements, reliability, threshold, maxIter)
    N = size(displacements, 1);
    if N < 3
        avgDisp = mean(displacements);
        inliers = 1:N;
        reliability = ones(1,N);
        return;
    end
    
    bestModel = [0, 0];
    bestCount = 0;
    weights = reliability / sum(reliability);  % 基于可靠性的权重
    
    for iter = 1:maxIter
        idx = randsample(N, 3, true, weights);  % 带权重采样
        sample = displacements(idx, :);
        model = weightedMean(sample, weights(idx));  % 估计模型（平移）
        errors = sqrt(sum((displacements - repmat(model, N, 1)).^2, 2));  % 误差计算
        inliers = errors < threshold;
        currentCount = sum(inliers .* reliability');  % 加权计数
        
        if currentCount > bestCount
            bestCount = currentCount;
            bestModel = model;
        end
    end
    % 重新计算内点并优化模型
    errors = sqrt(sum((displacements - repmat(bestModel, N, 1)).^2, 2));
    inliers = errors < threshold * 1.5;  % 进一步放宽阈值
    avgDisp = weightedMean(displacements(inliers,:), weights(inliers));  % 加权平均内点
    reliability = max(0.1, 1 - errors / (max(errors) + eps));  % 更新可靠性
end

%% 漂移校正函数（优化校正强度）
function [correctedGlobalMotion, drift] = correctDrift(globalMotion, featCurrentPos, featPoints, scaleFactor, frameIdx)
    pixelDrift = mean(featCurrentPos - featPoints, 1);  % 平均漂移（像素）
    drift = pixelDrift * scaleFactor;  % 转换为物理单位
    maxDrift = 15;  % 降低最大漂移校正量
    drift = sign(drift) .* min(abs(drift), maxDrift);
    correctedGlobalMotion = globalMotion - drift * (frameIdx / 150);  % 增强校正强度
    fprintf('第%d帧漂移校正: X-%.2f%s, Y-%.2f%s\n', frameIdx, drift(1), unit, drift(2), unit);
end

%% 位移平滑函数（保留更多细节）
function smoothedDisp = smoothDisplacement(disp, windowSize, frameIdx, prevSmoothed, history)
    if frameIdx == 1
        smoothedDisp = disp;
    else
        if frameIdx <= windowSize
            history(1:frameIdx) = disp;
            smoothedDisp = mean(history(1:frameIdx));
        else
            history = [history(2:end), disp];
            smoothedDisp = mean(history) * 0.8 + prevSmoothed * 0.2;  % 降低历史权重
        end
    end
end

%% 图像刚体位移修正函数（修复：简化参数和插值）
function correctedImg = correctRigidMotion(img, dx, dy)
    % dx/dy: 当前帧的像素位移（无人机当前晃动量）
    [h, w, c] = size(img);
    % 仿射变换矩阵：反向补偿当前位移
    tform = affine2d([1 0 0; 0 1 0; -dx -dy 1]);
    % 使用默认插值方法提高处理速度
    correctedImg = imwarp(img, tform, 'OutputView', imref2d([h w]));
end

%% 处理每帧图像（核心：优化跟踪逻辑）
fprintf('\n开始处理图像序列...\n');
hWait = waitbar(0, '处理中...');
dispHistoryX = cell(numStructurePoints, 1);
dispHistoryY = cell(numStructurePoints, 1);
for k = 1:numStructurePoints
    dispHistoryX{k} = zeros(1, displacementSmoothingWindow);
    dispHistoryY{k} = zeros(1, displacementSmoothingWindow);
end

for i = 1:numFrames
    waitbar(i/numFrames, hWait, sprintf('处理第 %d/%d 张...', i, numFrames));
    currentImage = imread(fullfile(imageFolder, imageFiles(i).name));
    if size(currentImage,3) == 1
        currentImage = cat(3, currentImage, currentImage, currentImage);
    end
    grayFrame = rgb2gray(currentImage);
    markedFrame = currentImage;  % 标记特征点的原始图像
    
    % 步骤1：跟踪外部特征点，估计无人机晃动（全局运动）
    for k = 1:numFeaturePoints
        prevPos = featCurrentPos(k,:);
        
        if i == 1
            % 第一帧无需跟踪，初始化位置
            featCurrentPos(k,:) = [prevPos(1), prevPos(2)];
            markedFrame = insertMarker(markedFrame, prevPos, 'o', 'Color', colorNames{k});
            continue;
        end
        
        % 多尺度匹配跟踪特征点
        [x_i, y_i, corrVal] = multiScaleMatching(grayFrame, prevPos, featTemplates(k,:), searchRange, templateSize);
        
        try
            if corrVal < corrThreshold * 0.7  % 降低失败阈值
                error(sprintf('相关系数过低（%.2f < %.2f）', corrVal, corrThreshold*0.7));
            end
            
            % 计算物理位移（毫米）
            absDispX = (x_i - featPoints(k,1)) * scaleFactor;
            absDispY = (y_i - featPoints(k,2)) * scaleFactor;
            
            % 异常值修正（放宽限制）
            maxFeatDisp = 40;  % 提高最大允许单帧位移
            if abs(absDispX - featFrameDisplacement(i-1,k,1)) > maxFeatDisp
                warning('特征点%d X方向位移异常，已修正', k);
                x_i = prevPos(1) + sign(absDispX - featFrameDisplacement(i-1,k,1)) * (maxFeatDisp / scaleFactor * 0.6);
                absDispX = featFrameDisplacement(i-1,k,1) + sign(absDispX - featFrameDisplacement(i-1,k,1)) * maxFeatDisp * 0.6;
            end
            if abs(absDispY - featFrameDisplacement(i-1,k,2)) > maxFeatDisp
                warning('特征点%d Y方向位移异常，已修正', k);
                y_i = prevPos(2) + sign(absDispY - featFrameDisplacement(i-1,k,2)) * (maxFeatDisp / scaleFactor * 0.6);
                absDispY = featFrameDisplacement(i-1,k,2) + sign(absDispY - featFrameDisplacement(i-1,k,2)) * maxFeatDisp * 0.6;
            end
            
            % 更新位移和位置
            featFrameDisplacement(i,k,:) = [absDispX, absDispY];
            % 平滑位置（降低平滑系数）
            smoothedX = motionSmoothing * x_i + (1 - motionSmoothing) * prevPos(1);
            smoothedY = motionSmoothing * y_i + (1 - motionSmoothing) * prevPos(2);
            smoothedX = max(1, min(smoothedX, frameW));
            smoothedY = max(1, min(smoothedY, frameH));
            featCurrentPos(k,:) = [smoothedX, smoothedY];
            
            % 模板更新（更频繁）
            if (mod(i, updateInterval) == 0 && corrVal > corrThreshold) || corrVal > 0.5
                for s = 1:length(templateSize)
                    ts = templateSize(s);
                    newTemplate = imcrop(grayFrame, [x_i-ts, y_i-ts, 2*ts, 2*ts]);
                    if size(newTemplate,1) == 2*ts && size(newTemplate,2) == 2*ts
                        featTemplates{k,s} = uint8(templateUpdateRate * double(newTemplate) + ...
                            (1 - templateUpdateRate) * double(featTemplates{k,s}));
                    end
                end
            end
            
            markedFrame = insertMarker(markedFrame, [x_i, y_i], 'o', 'Color', colorNames{k});
            featFailCount(k) = 0;
            featReliability(k) = min(1, featReliability(k) + 0.08);  % 更快提高可靠性
            
        catch ME
            % 处理跟踪失败
            featFailCount(k) = featFailCount(k) + 1;
            fprintf('第%d张图像特征点%d跟踪失败（连续%d次）: %s\n', i, k, featFailCount(k), ME.message);
            featReliability(k) = max(0.1, featReliability(k) - 0.1);  % 缓慢降低可靠性
            
            % 超过容忍次数则重定位（扩大搜索范围）
            if featFailCount(k) >= maxFailCount
                [x_i, y_i, corrVal] = multiScaleMatching(grayFrame, featPoints(k,:), featTemplates(k,:), searchRange*3, templateSize);
                featCurrentPos(k,:) = [x_i, y_i];
                absDispX = (x_i - featPoints(k,1)) * scaleFactor;
                absDispY = (y_i - featPoints(k,2)) * scaleFactor;
                featFrameDisplacement(i,k,:) = [absDispX, absDispY];
                featFailCount(k) = 0;
                featReliability(k) = 0.8;
                fprintf('特征点%d重定位成功，相关系数: %.2f\n', k, corrVal);
                
                % 重置模板
                for s = 1:length(templateSize)
                    ts = templateSize(s);
                    newTemplate = imcrop(grayFrame, [x_i-ts, y_i-ts, 2*ts, 2*ts]);
                    featTemplates{k,s} = newTemplate;
                end
            end
            
            markedFrame = insertMarker(markedFrame, featCurrentPos(k,:), 'o', 'Color', 'k');  % 黑色标记失败点
        end
    end
    
    % 步骤2：估计并平滑全局运动（无人机晃动）
    if i == 1
        globalMotion(i,:) = [0, 0];
        globalMotionSmoothed(i,:) = [0, 0];
    else
        % 筛选可靠特征点
        reliableIdx = featReliability > 0.3;  % 降低可靠阈值
        if sum(reliableIdx) < min(2, numFeaturePoints*0.5)
            warning('可靠特征点不足，使用全部特征点计算');
            dispMat = squeeze(featFrameDisplacement(i,:,:));
            [globalMotion_vec, inliers, ~] = ransacMotionEstimation(dispMat, ones(1,numFeaturePoints), 3.0, 600);
        else
            dispMat = squeeze(featFrameDisplacement(i,reliableIdx,:));
            [globalMotion_vec, inliers, featReliability(reliableIdx)] = ransacMotionEstimation(...
                dispMat, featReliability(reliableIdx), ransacThreshold, maxRANSACIter);
        end
        globalMotion(i,:) = globalMotion_vec;  % 单帧全局运动

        % 平滑全局运动（动态窗口）
        windowSize = min(7, i);  % 缩小窗口
        globalMotionSmoothed(1:i,:) = movmean(globalMotion(1:i,:), windowSize, 1);
        globalMotionSmoothed(i,:) = globalMotionSmoothed(i,:);

        % 漂移校正
        if mod(i, driftCorrectionInterval) == 0 && i > driftCorrectionInterval
            [globalMotionSmoothed(i,:), drift] = correctDrift(...
                globalMotionSmoothed(i,:), featCurrentPos, featPoints, scaleFactor, i);
            % 对历史帧进行轻微校正
            for j = max(1, i-driftCorrectionInterval/2):i
                w = exp(-0.1*(i-j));
                globalMotionSmoothed(j,:) = globalMotionSmoothed(j,:) - drift * w * 0.4;
            end
        end
    end

    % 步骤3：修正当前图像（修复：使用当前帧全局运动而非累积位移）
    if i == 1
        dx = 0;  % 第一帧无位移
        dy = 0;
        correctedFrame = currentImage;  % 第一帧无需修正
    else
        dx = globalMotionSmoothed(i,1) / scaleFactor;  % 当前帧像素位移
        dy = globalMotionSmoothed(i,2) / scaleFactor;
        correctedFrame = correctRigidMotion(currentImage, dx, dy);
    end
    
    % 步骤4：跟踪结构点，测量桥梁位移
    for k = 1:numStructurePoints
        prevPositions = cell2mat(strucPrevPos(k,:));
        velX = mean(diff(prevPositions(:,1)));  % 历史速度
        velY = mean(diff(prevPositions(:,2)));
        
        if i == 1
            predX = strucCurrentPos(k,1);
            predY = strucCurrentPos(k,2);
        else
            % 基于速度和加速度预测位置
            accX = velocityX(k) - velocityX(k);
            if abs(accX) > accelerationLimit
                accX = sign(accX) * accelerationLimit;
            end
            accY = velocityY(k) - velocityY(k);
            if abs(accY) > accelerationLimit
                accY = sign(accY) * accelerationLimit;
            end
            predX = strucCurrentPos(k,1) + (velocityX(k) + accX * 0.5) * 0.7;  % 调整预测权重
            predY = strucCurrentPos(k,2) + (velocityY(k) + accY * 0.5) * 0.7;
        end
        predX = max(1, min(round(predX), frameW));
        predY = max(1, min(round(predY), frameH));
        predPos = [predX, predY];
        
        if i == 1
            % 第一帧初始化
            strucFrameDisplacement(i,k,:) = [0, 0];
            absoluteDisplacement(i,k,:) = [0, 0];
            absoluteDisplacementSmoothed(i,k,:) = [0, 0];
            markedFrame = insertMarker(markedFrame, predPos, 's', 'Color', colorNames{numFeaturePoints + k});
            prevCorrVals(k,:) = 0.5;
            continue;
        end
        
        % 跟踪结构点
        [x_i, y_i, corrVal] = multiScaleMatching(grayFrame, predPos, strucTemplates(k,:), searchRange, templateSize);
        
        % 动态调整相关系数阈值（放宽限制）
        prevCorrVals(k,2:end) = prevCorrVals(k,1:end-1);
        prevCorrVals(k,1) = corrVal;
        meanPrevCorr = mean(prevCorrVals(k,:));
        currentThreshold = dynamicThreshold * max(corrThreshold * 0.6, min(meanPrevCorr * 0.5, 0.25)) + (~dynamicThreshold) * corrThreshold;
        
        try
            if corrVal < currentThreshold  % 二次搜索
                [x_i, y_i, corrVal] = multiScaleMatching(grayFrame, prevPos, strucTemplates(k,:), searchRange*2, templateSize);
                if corrVal < currentThreshold * 0.9  % 降低二次搜索阈值
                    error(sprintf('相关系数过低（%.2f < %.2f）', corrVal, currentThreshold*0.9));
                end
            end
            
            % 计算结构点物理位移
            absDispX = (x_i - strucPoints(k,1)) * scaleFactor;
            absDispY = (y_i - strucPoints(k,2)) * scaleFactor;
            strucFrameDisplacement(i,k,:) = [absDispX, absDispY];
            
            % 计算消除无人机晃动后的绝对位移
            rawAbsDispX = absDispX - globalMotionSmoothed(i,1);
            rawAbsDispY = absDispY - globalMotionSmoothed(i,2);
            
            % 平滑位移
            dispHistoryX{k} = [dispHistoryX{k}(2:end), rawAbsDispX];
            dispHistoryY{k} = [dispHistoryY{k}(2:end), rawAbsDispY];
            smoothedDispX = smoothDisplacement(rawAbsDispX, displacementSmoothingWindow, i, absoluteDisplacementSmoothed(i-1,k,1), dispHistoryX{k});
            smoothedDispY = smoothDisplacement(rawAbsDispY, displacementSmoothingWindow, i, absoluteDisplacementSmoothed(i-1,k,2), dispHistoryY{k});
            
            absoluteDisplacement(i,k,:) = [rawAbsDispX, rawAbsDispY];
            absoluteDisplacementSmoothed(i,k,:) = [smoothedDispX, smoothedDispY];
            
            % 更新速度
            deltaX = rawAbsDispX - absoluteDisplacement(i-1,k,1);
            deltaY = rawAbsDispY - absoluteDisplacement(i-1,k,2);
            prevVelocityX = velocityX(k);
            prevVelocityY = velocityY(k);
            velocityX(k) = 0.8 * velocityX(k) + 0.2 * deltaX;  % 调整速度更新权重
            velocityY(k) = 0.8 * velocityY(k) + 0.2 * deltaY;
            
            % 速度和加速度限制
            if abs(velocityX(k) - prevVelocityX) > accelerationLimit
                velocityX(k) = prevVelocityX + sign(velocityX(k) - prevVelocityX) * accelerationLimit;
            end
            if abs(velocityY(k) - prevVelocityY) > accelerationLimit
                velocityY(k) = prevVelocityY + sign(velocityY(k) - prevVelocityY) * accelerationLimit;
            end
            if abs(velocityX(k)) > maxVelocity
                velocityX(k) = sign(velocityX(k)) * maxVelocity;
            end
            if abs(velocityY(k)) > maxVelocity
                velocityY(k) = sign(velocityY(k)) * maxVelocity;
            end
            
            % 更新历史位置和模板（更频繁）
            for p = 5:-1:2
                strucPrevPos{k,p} = strucPrevPos{k,p-1};
            end
            strucPrevPos{k,1} = [x_i, y_i];
            strucCurrentPos(k,:) = [x_i, y_i];
            
            if (mod(i, updateInterval) == 0 && corrVal > currentThreshold) || (corrVal > 0.35)
                for s = 1:length(templateSize)
                    ts = templateSize(s);
                    newTemplate = imcrop(grayFrame, [x_i-ts, y_i-ts, 2*ts, 2*ts]);
                    if size(newTemplate,1) == 2*ts && size(newTemplate,2) == 2*ts
                        strucTemplates{k,s} = uint8(templateUpdateRate * double(newTemplate) + ...
                            (1 - templateUpdateRate) * double(strucTemplates{k,s}));
                    end
                end
            end
            
            % 标记结构点和位移信息（修复：使用正确的坐标系）
            markedFrame = insertMarker(markedFrame, [x_i, y_i], 's', 'Color', colorNames{numFeaturePoints + k});
            if i == 1
                correctedFrame = insertMarker(correctedFrame, [x_i, y_i], 's', 'Color', colorNames{numFeaturePoints + k});
            else
                correctedFrame = insertMarker(correctedFrame, [x_i - dx, y_i - dy], 's', 'Color', colorNames{numFeaturePoints + k});
            end
            dispText = sprintf('结构点%d: X=%.1f, Y=%.1f%s', k, smoothedDispX, smoothedDispY, unit);
            markedFrame = insertText(markedFrame, [x_i+15, y_i], dispText, 'TextColor', colorNames{numFeaturePoints + k}, 'FontSize', 10, 'Font', 'SimHei');
            if i == 1
                correctedFrame = insertText(correctedFrame, [x_i+15, y_i], dispText, 'TextColor', colorNames{numFeaturePoints + k}, 'FontSize', 10, 'Font', 'SimHei');
            else
                correctedFrame = insertText(correctedFrame, [x_i+15 - dx, y_i - dy], dispText, 'TextColor', colorNames{numFeaturePoints + k}, 'FontSize', 10, 'Font', 'SimHei');
            end
            
            strucFailCount(k) = 0;
            
        catch ME
            % 结构点跟踪失败处理
            strucFailCount(k) = strucFailCount(k) + 1;
            fprintf('第%d张图像结构点%d跟踪失败（连续%d次）: %s\n', i, k, strucFailCount(k), ME.message);
            
            if strucFailCount(k) >= maxFailCount  % 重定位（更大搜索范围）
                [x_i, y_i, corrVal] = multiScaleMatching(grayFrame, strucPoints(k,:), strucTemplates(k,:), searchRange*4, templateSize);
                strucCurrentPos(k,:) = [x_i, y_i];
                for p = 1:5
                    strucPrevPos{k,p} = [x_i, y_i];
                end
                absDispX = (x_i - strucPoints(k,1)) * scaleFactor;
                absDispY = (y_i - strucPoints(k,2)) * scaleFactor;
                strucFrameDisplacement(i,k,:) = [absDispX, absDispY];
                rawAbsDispX = absDispX - globalMotionSmoothed(i,1);
                rawAbsDispY = absDispY - globalMotionSmoothed(i,2);
                absoluteDisplacement(i,k,:) = [rawAbsDispX, rawAbsDispY];
                absoluteDisplacementSmoothed(i,k,:) = 0.7*absoluteDisplacementSmoothed(i-1,k,:) + 0.3*reshape([rawAbsDispX, rawAbsDispY], [1,1,2]);
                
                velocityX(k) = velocityX(k) * 0.4;  % 调整速度权重
                velocityY(k) = velocityY(k) * 0.4;
                strucFailCount(k) = 0;
                fprintf('结构点%d重定位成功，相关系数: %.2f\n', k, corrVal);
                
                % 重置模板
                for s = 1:length(templateSize)
                    ts = templateSize(s);
                    newTemplate = imcrop(grayFrame, [x_i-ts, y_i-ts, 2*ts, 2*ts]);
                    strucTemplates{k,s} = newTemplate;
                end
            end
            
            markedFrame = insertMarker(markedFrame, strucCurrentPos(k,:), 's', 'Color', 'k');
            if i == 1
                correctedFrame = insertMarker(correctedFrame, strucCurrentPos(k,:), 's', 'Color', 'k');
            else
                correctedFrame = insertMarker(correctedFrame, [strucCurrentPos(k,1) - dx, strucCurrentPos(k,2) - dy], 's', 'Color', 'k');
            end
        end
    end
    
    % 保存图像
    markedImages{i} = markedFrame;
    correctedImages{i} = correctedFrame;
    if saveEveryFrame || i == numFrames
        imwrite(markedFrame, fullfile(savePath, sprintf('marked_frame_%d.jpg', i)));
        imwrite(correctedFrame, fullfile(savePath, sprintf('corrected_frame_%d.jpg', i)));
    end
end
close(hWait);
fprintf('图像处理完成! 修正后图像已保存至: %s\n', savePath);

%% 结果计算与可视化
maxDispX = zeros(numStructurePoints, 1);
maxDispY = zeros(numStructurePoints, 1);
maxIdxX = zeros(numStructurePoints, 1);
maxIdxY = zeros(numStructurePoints, 1);
for k = 1:numStructurePoints
    dispX = absoluteDisplacementSmoothed(:,k,1);
    dispY = absoluteDisplacementSmoothed(:,k,2);
    
    % 异常值处理（更宽松）
    windowSize = min(7, length(dispX)-1);
    [~, outliersX] = isoutlier(dispX, 'movmedian', windowSize, 'ThresholdFactor',3.0);
    [~, outliersY] = isoutlier(dispY, 'movmedian', windowSize, 'ThresholdFactor',3.0);
    outliersX = logical(outliersX(1:length(dispX)));
    outliersY = logical(outliersY(1:length(dispY)));
    
    if any(outliersX), dispX(outliersX) = NaN; end
    if any(outliersY), dispY(outliersY) = NaN; end
    
    % 填充缺失值
    dispX = fillmissing(dispX, 'movmean', 5);
    dispY = fillmissing(dispY, 'movmean', 5);
    
    % 计算最大位移
    [maxValX, maxIdxX(k)] = max(abs(dispX));
    maxDispX(k) = maxValX;
    [maxValY, maxIdxY(k)] = max(abs(dispY));
    maxDispY(k) = maxValY;
end

% 频谱分析
fs = frameRate;
L = numFrames;
P1X = cell(numStructurePoints, 1);
P1Y = cell(numStructurePoints, 1);
dominantFreqX = zeros(numStructurePoints, 1);
dominantFreqY = zeros(numStructurePoints, 1);

for k = 1:numStructurePoints
    dispX = absoluteDisplacementSmoothed(:,k,1);
    dispX = dispX - movmean(dispX, min(5, L-1));
    N_fft = max(2^nextpow2(L), 64);
    YX = fft(dispX, N_fft);
    P2X = abs(YX / L);
    P1X{k} = P2X(1:floor(N_fft/2)+1);
    P1X{k}(2:end-1) = 2*P1X{k}(2:end-1);
    f_k = fs*(0:length(P1X{k})-1)/N_fft;
    validFreqIdx = f_k >= 0.05 & f_k <= 10;
    
    if sum(validFreqIdx) > 1
        [~, freqIdX] = max(P1X{k}(validFreqIdx));
        dominantFreqX(k) = f_k(validFreqIdx(freqIdX));
    else
        [~, freqIdX] = max(P1X{k}(2:end));
        dominantFreqX(k) = f_k(freqIdX+1);
    end
    
    dispY = absoluteDisplacementSmoothed(:,k,2);
    dispY = dispY - movmean(dispY, min(5, L-1));
    YY = fft(dispY, N_fft);
    P2Y = abs(YY / L);
    P1Y{k} = P2Y(1:floor(N_fft/2)+1);
    P1Y{k}(2:end-1) = 2*P1Y{k}(2:end-1);
    f_k = fs*(0:length(P1Y{k})-1)/N_fft;
    validFreqIdx = f_k >= 0.05 & f_k <= 10;
    
    if sum(validFreqIdx) > 1
        [~, freqIdY] = max(P1Y{k}(validFreqIdx));
        dominantFreqY(k) = f_k(validFreqIdx(freqIdY));
    else
        [~, freqIdY] = max(P1Y{k}(2:end));
        dominantFreqY(k) = f_k(freqIdY+1);
    end
end

%% 位移曲线绘图
figure('Position', [100, 100, 1400, 1000], 'Name', '位移分析结果', 'NumberTitle', 'off');
subplot(4,1,3);
hold on; grid on;
plot(1:numFrames, zeros(1,numFrames), 'k-.', 'LineWidth', 1);
for k = 1:numStructurePoints
    plot(1:numFrames, absoluteDisplacementSmoothed(:,k,1), 'Color', colorNames{numFeaturePoints + k}, 'LineWidth', 1.5);
    plot(maxIdxX(k), absoluteDisplacementSmoothed(maxIdxX(k),k,1), 'mo', 'MarkerSize', 8);
    text(maxIdxX(k), absoluteDisplacementSmoothed(maxIdxX(k),k,1), ...
        sprintf('  最大X: %.2f%s', maxDispX(k), unit), 'Color','m', 'FontName', 'SimHei');
end
title(['X方向绝对位移(消除晃动后)(', unit, ')']);
xlabel('图像序号'); ylabel(['位移(', unit, ')']);

subplot(4,1,4);
hold on; grid on;
plot(1:numFrames, zeros(1,numFrames), 'k-.', 'LineWidth', 1);
for k = 1:numStructurePoints
    plot(1:numFrames, absoluteDisplacementSmoothed(:,k,2), 'Color', colorNames{numFeaturePoints + k}, 'LineWidth', 1.5);
    plot(maxIdxY(k), absoluteDisplacementSmoothed(maxIdxY(k),k,2), 'mo', 'MarkerSize', 8);
    text(maxIdxY(k), absoluteDisplacementSmoothed(maxIdxY(k),k,2), ...
        sprintf('  最大Y: %.2f%s', maxDispY(k), unit), 'Color','m', 'FontName', 'SimHei');
end
title(['Y方向绝对位移(消除晃动后)(', unit, ')']);
xlabel('图像序号'); ylabel(['位移(', unit, ')']);

%% 频谱分析绘图
figure('Position', [100, 100, 1400, 600], 'Name','位移频谱分析','NumberTitle','off');
subplot(2,1,1);
hold on; grid on;
plot([0.05, 0.05], ylim, 'g--', 'LineWidth', 1);
plot([10, 10], ylim, 'g--', 'LineWidth', 1);
for k = 1:numStructurePoints
    f_k = fs*(0:length(P1X{k})-1)/N_fft;
    plot(f_k, P1X{k}, 'Color', colorNames{numFeaturePoints + k}, 'LineWidth', 1.5);
    if dominantFreqX(k) > 0.05 && ~isnan(dominantFreqX(k))
        [~, closestIdx] = min(abs(f_k - dominantFreqX(k)));
        plot(f_k(closestIdx), P1X{k}(closestIdx), 'mo', 'MarkerSize', 8);
        text(f_k(closestIdx), P1X{k}(closestIdx), ...
            sprintf('  主频X: %.4fHz', dominantFreqX(k)), 'Color','m', 'FontName', 'SimHei');
    end
end
title('X方向位移频谱');
xlabel('频率(Hz)'); ylabel('幅度');
xlim([0, 15]);

subplot(2,1,2);
hold on; grid on;
plot([0.05, 0.05], ylim, 'g--', 'LineWidth', 1);
plot([10, 10], ylim, 'g--', 'LineWidth', 1);
for k = 1:numStructurePoints
    f_k = fs*(0:length(P1Y{k})-1)/N_fft;
    plot(f_k, P1Y{k}, 'Color', colorNames{numFeaturePoints + k}, 'LineWidth', 1.5);
    if dominantFreqY(k) > 0.05 && ~isnan(dominantFreqY(k))
        [~, closestIdx] = min(abs(f_k - dominantFreqY(k)));
        plot(f_k(closestIdx), P1Y{k}(closestIdx), 'mo', 'MarkerSize', 8);
        text(f_k(closestIdx), P1Y{k}(closestIdx), ...
            sprintf('  主频Y: %.4fHz', dominantFreqY(k)), 'Color','m', 'FontName', 'SimHei');
    end
end
title('Y方向位移频谱');
xlabel('频率(Hz)'); ylabel('幅度');
xlim([0, 15]);

%% 保存结果
save(fullfile(savePath, 'displacement_results.mat'), ...
    'absoluteDisplacement', 'absoluteDisplacementSmoothed', 'correctedImages', ...
    'scaleFactor', 'frameRate', 'numFrames', 'dominantFreqX', 'dominantFreqY', ...
    'maxDispX', 'maxDispY', 'markedImages');
fprintf('结果数据已保存至: %s\n', fullfile(savePath, 'displacement_results.mat'));

%% 显示关键结果
fprintf('\n=== 分析结果 ===\n');
for k = 1:numStructurePoints
    fprintf('结构点%d:\n', k);
    fprintf('  X方向: 最大位移=%.4f%s, 主频率=%.4fHz\n', ...
        maxDispX(k), unit, dominantFreqX(k));
    fprintf('  Y方向: 最大位移=%.4f%s, 主频率=%.4fHz\n', ...
        maxDispY(k), unit, dominantFreqY(k));
end