% 生成DJI图像处理结果的可视化对比分析
clear all; close all; clc;

fprintf('=== 生成可视化对比分析 ===\n');

%% 加载分析数据
if exist('dji_analysis_results.mat', 'file')
    load('dji_analysis_results.mat');
    fprintf('✅ 加载分析数据成功\n');
else
    fprintf('❌ 未找到分析数据文件\n');
    return;
end

numFrames = length(analysisData.originalFiles);
globalMotion = analysisData.globalMotion;
globalMotionSmoothed = analysisData.globalMotionSmoothed;
motionReliability = analysisData.motionReliability;
stats = analysisData.statistics;

%% 生成综合分析图表
fprintf('生成综合分析图表...\n');

figure('Position', [100, 100, 1600, 1200], 'Name', 'DJI图像晃动补偿分析结果');

% 子图1：全局运动时间序列
subplot(3,2,1);
plot(1:numFrames, globalMotion(:,1), 'r--', 'LineWidth', 1.5, 'DisplayName', '原始X晃动');
hold on;
plot(1:numFrames, globalMotionSmoothed(:,1), 'r-', 'LineWidth', 2, 'DisplayName', '平滑X晃动');
plot(1:numFrames, globalMotion(:,2), 'b--', 'LineWidth', 1.5, 'DisplayName', '原始Y晃动');
plot(1:numFrames, globalMotionSmoothed(:,2), 'b-', 'LineWidth', 2, 'DisplayName', '平滑Y晃动');
xlabel('帧数'); ylabel('位移 (毫米)');
title('无人机全局运动（晃动）时间序列');
legend('Location', 'best');
grid on;

% 子图2：运动可靠性
subplot(3,2,2);
bar(2:numFrames, motionReliability(2:end), 'FaceColor', [0.3 0.7 0.3]);
xlabel('帧数'); ylabel('可靠性');
title('运动估计可靠性');
ylim([0 1]);
grid on;

% 子图3：位移修正量
subplot(3,2,3);
pixel_displacement_x = globalMotionSmoothed(:,1) / analysisData.scaleFactor;
pixel_displacement_y = globalMotionSmoothed(:,2) / analysisData.scaleFactor;
plot(1:numFrames, pixel_displacement_x, 'r-', 'LineWidth', 2, 'DisplayName', 'X方向修正');
hold on;
plot(1:numFrames, pixel_displacement_y, 'b-', 'LineWidth', 2, 'DisplayName', 'Y方向修正');
xlabel('帧数'); ylabel('像素位移');
title('图像修正位移量');
legend('Location', 'best');
grid on;

% 子图4：运动幅度分布
subplot(3,2,4);
motion_magnitude = sqrt(globalMotion(:,1).^2 + globalMotion(:,2).^2);
histogram(motion_magnitude, 10, 'FaceColor', [0.7 0.3 0.3], 'EdgeColor', 'black');
xlabel('运动幅度 (毫米)'); ylabel('频次');
title('运动幅度分布直方图');
grid on;

% 子图5：X-Y运动轨迹
subplot(3,2,5);
plot(globalMotion(:,1), globalMotion(:,2), 'ro-', 'LineWidth', 1.5, 'MarkerSize', 6);
hold on;
plot(globalMotionSmoothed(:,1), globalMotionSmoothed(:,2), 'bo-', 'LineWidth', 2, 'MarkerSize', 4);
xlabel('X方向位移 (毫米)'); ylabel('Y方向位移 (毫米)');
title('无人机运动轨迹');
legend({'原始轨迹', '平滑轨迹'}, 'Location', 'best');
grid on;
axis equal;

% 子图6：统计指标对比
subplot(3,2,6);
categories = {'X标准差', 'Y标准差', '最大X修正', '最大Y修正'};
values = [stats.motion_std_x, stats.motion_std_y, stats.max_correction_x, stats.max_correction_y];
bar(values, 'FaceColor', [0.5 0.5 0.8]);
set(gca, 'XTickLabel', categories);
ylabel('数值');
title('关键统计指标');
grid on;

% 保存图表
saveas(gcf, 'dji_motion_analysis.png');
fprintf('✅ 综合分析图表已保存: dji_motion_analysis.png\n');

%% 生成处理前后对比图
fprintf('生成处理前后对比图...\n');

% 选择几个代表性帧进行对比
compareFrames = [1, 4, 7, 10];  % 选择4个代表性帧
figure('Position', [200, 200, 1400, 800], 'Name', '处理前后图像对比');

for i = 1:length(compareFrames)
    frameIdx = compareFrames(i);
    
    % 读取原始图像和修正图像
    originalImg = imread(analysisData.originalFiles{frameIdx});
    correctedImg = imread(fullfile('dji_corrected_images', analysisData.correctedFiles{frameIdx}));
    
    % 显示原始图像
    subplot(2, length(compareFrames), i);
    imshow(imresize(originalImg, 0.3));  % 缩小显示
    title(sprintf('原始帧%d', frameIdx));
    
    % 显示修正图像
    subplot(2, length(compareFrames), i + length(compareFrames));
    imshow(imresize(correctedImg, 0.3));  % 缩小显示
    title(sprintf('修正帧%d', frameIdx));
end

% 保存对比图
saveas(gcf, 'dji_before_after_comparison.png');
fprintf('✅ 处理前后对比图已保存: dji_before_after_comparison.png\n');

%% 生成详细统计报告
fprintf('生成详细统计报告...\n');

reportFile = 'DJI_处理结果详细报告.txt';
fid = fopen(reportFile, 'w');

fprintf(fid, '=== DJI无人机图像晃动补偿处理详细报告 ===\n\n');
fprintf(fid, '处理时间: %s\n', analysisData.processingTime);
fprintf(fid, '处理图像数量: %d 张\n', numFrames);
fprintf(fid, '尺度因子: %.2f 毫米/像素\n\n', analysisData.scaleFactor);

fprintf(fid, '=== 全局运动统计 ===\n');
fprintf(fid, 'X方向运动范围: [%.3f, %.3f] 毫米\n', stats.motion_range_x(1), stats.motion_range_x(2));
fprintf(fid, 'Y方向运动范围: [%.3f, %.3f] 毫米\n', stats.motion_range_y(1), stats.motion_range_y(2));
fprintf(fid, 'X方向标准差: %.3f 毫米\n', stats.motion_std_x);
fprintf(fid, 'Y方向标准差: %.3f 毫米\n', stats.motion_std_y);
fprintf(fid, 'X方向平均值: %.3f 毫米\n', stats.motion_mean_x);
fprintf(fid, 'Y方向平均值: %.3f 毫米\n', stats.motion_mean_y);
fprintf(fid, '平滑后X方向标准差: %.3f 毫米\n', stats.smoothed_std_x);
fprintf(fid, '平滑后Y方向标准差: %.3f 毫米\n', stats.smoothed_std_y);
fprintf(fid, '平均运动估计可靠性: %.3f\n\n', stats.avg_reliability);

fprintf(fid, '=== 图像修正统计 ===\n');
fprintf(fid, 'X方向最大修正: %.2f 像素\n', stats.max_correction_x);
fprintf(fid, 'Y方向最大修正: %.2f 像素\n', stats.max_correction_y);
fprintf(fid, '平均修正幅度: %.2f 像素\n\n', stats.avg_correction);

fprintf(fid, '=== 逐帧运动数据 ===\n');
fprintf(fid, '帧号\t原始X(mm)\t原始Y(mm)\t平滑X(mm)\t平滑Y(mm)\t可靠性\n');
for i = 1:numFrames
    fprintf(fid, '%d\t%.3f\t\t%.3f\t\t%.3f\t\t%.3f\t\t%.3f\n', ...
        i, globalMotion(i,1), globalMotion(i,2), ...
        globalMotionSmoothed(i,1), globalMotionSmoothed(i,2), motionReliability(i));
end

fprintf(fid, '\n=== 处理文件列表 ===\n');
fprintf(fid, '原始文件:\n');
for i = 1:numFrames
    fprintf(fid, '  %d. %s\n', i, analysisData.originalFiles{i});
end
fprintf(fid, '\n修正文件:\n');
for i = 1:numFrames
    fprintf(fid, '  %d. %s\n', i, analysisData.correctedFiles{i});
end

fclose(fid);
fprintf('✅ 详细统计报告已保存: %s\n', reportFile);

%% 生成性能评估
fprintf('生成性能评估...\n');

% 计算处理效果指标
smoothing_improvement_x = (stats.motion_std_x - stats.smoothed_std_x) / stats.motion_std_x * 100;
smoothing_improvement_y = (stats.motion_std_y - stats.smoothed_std_y) / stats.motion_std_y * 100;

fprintf('\n=== 性能评估结果 ===\n');
fprintf('平滑处理改善效果:\n');
fprintf('  X方向标准差改善: %.1f%%\n', smoothing_improvement_x);
fprintf('  Y方向标准差改善: %.1f%%\n', smoothing_improvement_y);
fprintf('  运动估计平均可靠性: %.1f%%\n', stats.avg_reliability * 100);

if stats.avg_reliability > 0.6
    fprintf('✅ 运动估计质量: 良好\n');
elseif stats.avg_reliability > 0.4
    fprintf('⚠️  运动估计质量: 中等\n');
else
    fprintf('❌ 运动估计质量: 较差\n');
end

if stats.avg_correction < 10
    fprintf('✅ 修正幅度: 合理范围\n');
elseif stats.avg_correction < 20
    fprintf('⚠️  修正幅度: 中等偏大\n');
else
    fprintf('❌ 修正幅度: 过大，可能存在问题\n');
end

fprintf('\n=== 可视化分析完成 ===\n');
fprintf('✅ 综合分析图表: dji_motion_analysis.png\n');
fprintf('✅ 处理前后对比: dji_before_after_comparison.png\n');
fprintf('✅ 详细统计报告: %s\n', reportFile);
fprintf('✅ 性能评估: 完成\n');
