% 处理真实的DJI无人机图像文件
% 使用修复后的桥梁结构监测代码逻辑
clear all; close all; clc;

fprintf('=== 处理真实的DJI无人机图像 ===\n');

%% 第1步：读取DJI图像序列
imageFolder = '.';  % 当前目录
imageExt = 'DJI_*.JPG';  % DJI图像文件模式
savePath = fullfile(imageFolder, 'dji_corrected_images');
if ~exist(savePath, 'dir'), mkdir(savePath); end

% 读取DJI图像序列
imageFiles = dir(fullfile(imageFolder, imageExt));
numFrames = length(imageFiles);

fprintf('找到 %d 张DJI图像:\n', numFrames);
for i = 1:numFrames
    fprintf('  %d. %s (%.2f MB)\n', i, imageFiles(i).name, imageFiles(i).bytes/(1024*1024));
end

if numFrames < 2
    error('图像数量过少（至少需要2张）');
end

% 按文件名排序确保正确顺序
[~, idx] = sort({imageFiles.name});
imageFiles = imageFiles(idx);

% 读取第一张图像获取尺寸信息
frame1 = imread(fullfile(imageFolder, imageFiles(1).name));
[frameH, frameW, frameC] = size(frame1);
fprintf('\n图像信息:\n');
fprintf('  尺寸: %dx%dx%d\n', frameH, frameW, frameC);
fprintf('  数据类型: %s\n', class(frame1));
fprintf('  像素值范围: [%d, %d]\n', min(frame1(:)), max(frame1(:)));

%% 第2步：读取所有图像到内存
fprintf('\n读取所有DJI图像到内存...\n');
originalImages = cell(numFrames, 1);
grayImages = cell(numFrames, 1);

for i = 1:numFrames
    img = imread(fullfile(imageFolder, imageFiles(i).name));
    if size(img,3) == 1
        img = cat(3, img, img, img);  % 转为RGB
    end
    originalImages{i} = img;
    grayImages{i} = rgb2gray(img);
    fprintf('  读取图像 %d/%d: %s\n', i, numFrames, imageFiles(i).name);
end

%% 第3步：简化的特征点跟踪（基于图像相关性）
fprintf('\n进行简化的全局运动估计...\n');

% 设置参数
scaleFactor = 1.0;  % 假设1像素=1毫米（可根据实际情况调整）
templateSize = 64;  % 模板大小
numSamplePoints = 9;  % 采样点数量

% 初始化变量
globalMotion = zeros(numFrames, 2);
globalMotionSmoothed = zeros(numFrames, 2);

% 在第一帧中选择采样点（网格分布）
samplePoints = [];
stepX = floor(frameW / 4);
stepY = floor(frameH / 4);
for y = stepY:stepY:(frameH-stepY)
    for x = stepX:stepX:(frameW-stepX)
        samplePoints = [samplePoints; x, y];
    end
end
numSamplePoints = size(samplePoints, 1);
fprintf('  选择了 %d 个采样点进行运动估计\n', numSamplePoints);

% 逐帧计算全局运动
for i = 2:numFrames
    fprintf('  处理帧 %d/%d...', i, numFrames);
    
    prevGray = grayImages{i-1};
    currGray = grayImages{i};
    
    displacements = [];
    
    % 对每个采样点进行模板匹配
    for j = 1:numSamplePoints
        x = samplePoints(j, 1);
        y = samplePoints(j, 2);
        
        % 确保模板在图像范围内
        if x-templateSize/2 > 0 && x+templateSize/2 <= frameW && ...
           y-templateSize/2 > 0 && y+templateSize/2 <= frameH
            
            % 提取模板
            template = prevGray(y-templateSize/2+1:y+templateSize/2, ...
                               x-templateSize/2+1:x+templateSize/2);
            
            % 在当前帧中搜索
            searchRange = 30;  % 搜索范围
            minX = max(1, x-searchRange);
            maxX = min(frameW-templateSize+1, x+searchRange);
            minY = max(1, y-searchRange);
            maxY = min(frameH-templateSize+1, y+searchRange);
            
            if maxX > minX && maxY > minY
                searchArea = currGray(minY:maxY+templateSize-1, minX:maxX+templateSize-1);
                
                % 模板匹配
                corr = normxcorr2(template, searchArea);
                [~, maxIdx] = max(corr(:));
                [maxY_local, maxX_local] = ind2sub(size(corr), maxIdx);
                
                % 计算位移
                dx = (minX + maxX_local - templateSize) - x;
                dy = (minY + maxY_local - templateSize) - y;
                
                displacements = [displacements; dx, dy];
            end
        end
    end
    
    % 计算全局运动（使用中位数减少异常值影响）
    if ~isempty(displacements)
        globalMotion(i, 1) = median(displacements(:, 1)) * scaleFactor;
        globalMotion(i, 2) = median(displacements(:, 2)) * scaleFactor;
    else
        globalMotion(i, :) = [0, 0];
    end
    
    fprintf(' 全局运动: [%.2f, %.2f] 毫米\n', globalMotion(i, 1), globalMotion(i, 2));
end

% 应用平滑处理
windowSize = 3;
for i = 1:numFrames
    if i == 1
        globalMotionSmoothed(i, :) = [0, 0];
    else
        startIdx = max(1, i - windowSize + 1);
        globalMotionSmoothed(i, :) = mean(globalMotion(startIdx:i, :), 1);
    end
end

fprintf('✓ 全局运动估计完成\n');
fprintf('  X方向晃动范围: [%.2f, %.2f] 毫米\n', min(globalMotion(:,1)), max(globalMotion(:,1)));
fprintf('  Y方向晃动范围: [%.2f, %.2f] 毫米\n', min(globalMotion(:,2)), max(globalMotion(:,2)));

%% 第4步：应用修复后的图像修正逻辑
fprintf('\n应用修复后的图像修正逻辑...\n');
correctedImages = cell(numFrames, 1);

for i = 1:numFrames
    currentImage = originalImages{i};
    
    % 修复后的逻辑：使用当前帧全局运动而非累积位移
    if i == 1
        dx = 0;  % 第一帧无位移
        dy = 0;
        correctedImages{i} = currentImage;  % 第一帧无需修正
        fprintf('  帧%d: 第一帧，无需修正\n', i);
    else
        dx = globalMotionSmoothed(i,1) / scaleFactor;  % 当前帧像素位移
        dy = globalMotionSmoothed(i,2) / scaleFactor;
        
        % 应用仿射变换进行图像修正
        [h, w, c] = size(currentImage);
        tform = affine2d([1 0 0; 0 1 0; -dx -dy 1]);
        correctedImages{i} = imwarp(currentImage, tform, 'OutputView', imref2d([h w]));
        
        fprintf('  帧%d: 位移[%.2f, %.2f]像素 -> 修正完成\n', i, dx, dy);
    end
end

fprintf('✓ 完成%d帧DJI图像的晃动修正\n', numFrames);

%% 第5步：保存消除晃动后的图像
fprintf('\n保存消除晃动后的DJI图像...\n');

for i = 1:numFrames
    % 生成输出文件名
    [~, baseName, ~] = fileparts(imageFiles(i).name);
    outputName = sprintf('%s_corrected.jpg', baseName);
    outputPath = fullfile(savePath, outputName);
    
    % 保存修正后的图像
    imwrite(correctedImages{i}, outputPath, 'Quality', 95);
    fprintf('  保存: %s\n', outputName);
end

fprintf('✓ 成功保存%d张消除晃动后的DJI图像到 %s 文件夹\n', numFrames, savePath);

%% 第6步：生成处理结果统计
fprintf('\n=== DJI图像处理结果统计 ===\n');

% 计算晃动统计
original_std_x = std(globalMotion(:,1));
smoothed_std_x = std(globalMotionSmoothed(:,1));
original_std_y = std(globalMotion(:,2));
smoothed_std_y = std(globalMotionSmoothed(:,2));

fprintf('晃动数据统计:\n');
fprintf('  X方向原始标准差: %.3f 毫米\n', original_std_x);
fprintf('  X方向平滑标准差: %.3f 毫米\n', smoothed_std_x);
fprintf('  Y方向原始标准差: %.3f 毫米\n', original_std_y);
fprintf('  Y方向平滑标准差: %.3f 毫米\n', smoothed_std_y);

% 计算修正量统计
pixel_displacement_x = globalMotionSmoothed(:,1) / scaleFactor;
pixel_displacement_y = globalMotionSmoothed(:,2) / scaleFactor;

fprintf('\n图像修正统计:\n');
fprintf('  X方向最大修正: %.2f 像素\n', max(abs(pixel_displacement_x)));
fprintf('  Y方向最大修正: %.2f 像素\n', max(abs(pixel_displacement_y)));
fprintf('  平均修正幅度: %.2f 像素\n', mean(sqrt(pixel_displacement_x.^2 + pixel_displacement_y.^2)));

% 显示处理的文件列表
fprintf('\n处理完成的文件列表:\n');
correctedFiles = dir(fullfile(savePath, '*_corrected.jpg'));
for i = 1:length(correctedFiles)
    fprintf('  %d. %s (%.2f MB)\n', i, correctedFiles(i).name, correctedFiles(i).bytes/(1024*1024));
end

fprintf('\n🎉 DJI图像晃动补偿处理完成！\n');
fprintf('原始图像: %d 张\n', numFrames);
fprintf('修正图像: %d 张\n', length(correctedFiles));
fprintf('保存目录: %s\n', savePath);
